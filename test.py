# 通过selenium打开Chrome浏览器用户扫码登录后页面定时刷新给token保活
# pip install selenium
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
from selenium.webdriver import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import json

chrome_option = Options()

chrome_option.set_capability("goog:loggingPrefs", {"performance": "ALL"})

driver = webdriver.Chrome(options=chrome_option)
driver.get("https://mp.weixin.qq.com")  # 用户登录后自己扫码

locator = (By.ID, "js_index_menu")  # 判断用户是否扫码加载出了公众号菜单列表

WebDriverWait(driver, 120, 0.5).until(
    EC.presence_of_element_located(locator)
)  # 等待120秒，120秒内未扫码失效
print("login")
wait = WebDriverWait(driver, 10)
element = driver.find_element(By.ID, "js_index_menu")


def getNewToken(driver):
    driver.refresh()  # 刷新页面
    print(
        "----------------------------打印cookies信息----------------------------------------"
    )
    # 获取当前页面的所有cookie
    cookies = driver.get_cookies()
    agent = driver.execute_script("return navigator.userAgent")
    token = ""
    cookieValue = ""
    for cookie in cookies:
        cookieValue = cookieValue + cookie["name"] + "=" + cookie["value"] + ";"

    # 获取网络日志
    logs = driver.get_log("performance")

    for item in logs:
        StrMsg = item["message"]
        data = json.loads(StrMsg)
        method = data["message"]["method"]
        if method == "Network.responseReceived":
            tokenUrl = data["message"]["params"]["response"]["url"]
            if "cgi-bin/home" in tokenUrl:
                print(tokenUrl)
                parts = tokenUrl.split("&")
                for cs in parts:
                    if "token" in cs:
                        tokenArray = cs.split("=")
                        token = tokenArray[1]
                        break

    return agent, cookieValue, token


while True:
    strings = getNewToken(driver)
    print(strings[0])  # useragent
    print(strings[1])  # cookieValue
    print(strings[2])  # token
    # 要保存的文本数据
    userMsg = strings[0] + "*" + strings[1] + "*" + strings[2]
    # 使用with语句打开一个文件，'w'表示写入模式，会覆盖已有文件或创建新文件
    with open("userMsg.txt", "w", encoding="utf-8") as file:
        # 写入数据到文件
        file.write(userMsg)
    print("数据已成功写入文件")
    time.sleep(3600)  # 小时刷新一次token信息3600
