import random
from tokenize import String
from xmlrpc.client import Boolean
import requests
import json
import datetime
import time
import gzip
import urllib.parse
import urllib.error
from typing import Union, Dict, Any, List, Optional
import urllib.request
import urllib.parse
import subprocess



no_proxy_handler = urllib.request.ProxyHandler({})
opener = urllib.request.build_opener(no_proxy_handler)

def get_current_date_str(live_id):
    now = datetime.datetime.now()
    current_hour = now.hour
    date_str = now.strftime("%Y-%m-%d")
    if current_hour < 12:
        date_str += "-a-"
    else:
        date_str += "-p-"
    date_str += live_id
    return date_str


def loop_until_status_one(url, date_str):
    count = 0
    while True:
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            time.sleep(1)  # Add a small delay before retrying
            continue

        status = data.get("data", {}).get("item", {}).get("status", None)
        origin = (
            data.get("data", {})
            .get("item", {})
            .get("stream_info", {})
            .get("replay_url", {})
            .get("origin", None)
        )

        if origin is not None:
            formatted_data = json.dumps(
                data, ensure_ascii=False, indent=4, sort_keys=True
            )
            log_path = f"./log/{date_str}.json"
            with open(log_path, "w", encoding="utf-8") as f:
                f.write(formatted_data)
            # dingtalk(dingWebHook(), origin)
            print(f"{datetime.datetime.now()} Data saved to {log_path}")
            # print(origin)
            download_m3u8(origin, date_str)
            break

        if status == 2:
            print("Status is 2, exiting loop.")
            break

        time.sleep(2)  # Avoid frequent requests
        count += 1
        print(f"Attempt {count} {date_str}\r", end="")


def get_req(
    url: str,
    proxy_addr: Union[str, None] = None,
    headers: Union[dict, None] = None,
    data: Union[dict, bytes, None] = None,
    json_data: Union[dict, list, None] = None,
    timeout: int = 20,
    abroad: bool = False,
    content_conding: str = "utf-8",
    redirect_url: bool = False,
) -> Union[str, Any]:
    if headers is None:
        headers = {}
    try:
        if proxy_addr:
            proxies = {"http": proxy_addr, "https": proxy_addr}
            if data or json_data:
                response = requests.post(
                    url,
                    data=data,
                    json=json_data,
                    headers=headers,
                    proxies=proxies,
                    timeout=timeout,
                )
            else:
                response = requests.get(
                    url, headers=headers, proxies=proxies, timeout=timeout
                )
            if redirect_url:
                return response.url
            resp_str = response.text
        else:
            if data and not isinstance(data, bytes):
                data = urllib.parse.urlencode(data).encode(content_conding)
            if json_data and isinstance(json_data, (dict, list)):
                data = json.dumps(json_data).encode(content_conding)

            req = urllib.request.Request(url, data=data, headers=headers)

            try:
                if abroad:
                    response = urllib.request.urlopen(req, timeout=timeout)
                else:
                    response = opener.open(req, timeout=timeout)
                if redirect_url:
                    return response.url
                content_encoding = response.info().get("Content-Encoding")
                try:
                    if content_encoding == "gzip":
                        with gzip.open(
                            response, "rt", encoding=content_conding
                        ) as gzipped:
                            resp_str = gzipped.read()
                    else:
                        resp_str = response.read().decode(content_conding)
                finally:
                    response.close()

            except urllib.error.HTTPError as e:
                if e.code == 400:
                    resp_str = e.read().decode(content_conding)
                else:
                    raise
            except urllib.error.URLError as e:
                print("URL Error:", e)
                raise
            except Exception as e:
                print("An error occurred:", e)
                raise

    except Exception as e:
        resp_str = str(e)

    return resp_str


def get_weibo_stream_data(
    url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None
) -> Dict[str, Any]:
    headers = {
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Cookie": "XSRF-TOKEN=qAP-pIY5V4tO6blNOhA4IIOD; SUB=_2AkMRNMCwf8NxqwFRmfwWymPrbI9-zgzEieKnaDFrJRMxHRl-yT9kqmkhtRB6OrTuX5z9N_7qk9C3xxEmNR-8WLcyo2PM; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9WWemwcqkukCduUO11o9sBqA; WBPSESS=Wk6CxkYDejV3DDBcnx2LOXN9V1LjdSTNQPMbBDWe4lO2HbPmXG_coMffJ30T-Avn_ccQWtEYFcq9fab1p5RR6PEI6w661JcW7-56BszujMlaiAhLX-9vT4Zjboy1yf2l",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    }
    if cookies:
        headers["Cookie"] = cookies

    try:
        json_str = get_req(url, proxy_addr=proxy_addr, headers=headers)
        json_data = json.loads(json_str)
    except:
        print(json_str)
        time.sleep(random.randint(60, 300))
        return

    if "data" not in json_data:
        print(f"{datetime.datetime.now()} {json_str}")
        time.sleep(random.randint(600, 800))
        return
    for i in json_data["data"]["list"]:
        if "isTop" in i and i["isTop"] == 1:
            continue

        if i["isLongText"]:
            long_str = get_req(
                f'https://weibo.com/ajax/statuses/longtext?id={i["mblogid"]}',
                proxy_addr=proxy_addr,
                headers=headers,
            )
            # 获取结果存入long_text字段

        

        if "page_info" in i and i["page_info"]["object_type"] == "live":
            room_id = i["page_info"]["object_id"]
            loop_until_status_one(
                f"https://weibo.com/l/pc/anchor/live?live_id={room_id}",
                get_current_date_str(room_id.replace("1022:", "")),
            )
            continue
        elif (
            "page_info" in i
            and i["page_info"]["object_type"] != "video"
            and i["page_info"]["object_type"] != "live"
        ):
            print(i["page_info"]["object_type"])

        time.sleep(1)


def download_m3u8(url, filename):
    process = subprocess.Popen(
        [
            "D:/tools/m3u8/N_m3u8DL-CLI_v3.0.2.exe",
            url,
            "--workDir",
            "D:/tools/m3u8/冰寒直播",
            "--saveName",
            filename,
            "--noMerge",
            "--noProxy",
        ],
        stdout=subprocess.PIPE,
        text=True,
    )

    while True:
        line = process.stdout.readline()
        if not line:
            print("下载完成。。。")
            break
        # print(f'{line} \r', end='')
    process.wait()





if __name__ == "__main__":
    get_weibo_stream_data(
            url="https://weibo.com/ajax/statuses/mymblog?uid=5515166761&page=1&feature=0"
        )
      