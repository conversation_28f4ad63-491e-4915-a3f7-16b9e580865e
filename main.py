import random
from tokenize import String
from xmlrpc.client import Boolean
import requests
import json
import datetime
import time
import gzip
import urllib.parse
import urllib.error
from typing import Union, Dict, Any, List, Optional
import urllib.request
import urllib.parse
import hmac
import hashlib
import base64
import subprocess
import socket


no_proxy_handler = urllib.request.ProxyHandler({})
opener = urllib.request.build_opener(no_proxy_handler)


# 看涨速榜，关注到个股板块，看在板块的排名，是不是前排？ 大单曲线
# ffmpeg -i 20220908.mp4 -ss 01:02:45 -to 01:06:20 -c copy 20220908-5.mp4 截取
# 假设的API URL


def get_current_date_str(live_id):
    now = datetime.datetime.now()
    current_hour = now.hour
    date_str = now.strftime("%Y-%m-%d")
    if current_hour < 12:
        date_str += "-a-"
    else:
        date_str += "-p-"
    date_str += live_id
    return date_str


def loop_until_status_one(url, date_str):
    count = 0
    while True:
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
        except Exception as e:
            print(f"请求失败: {e}")
            time.sleep(1)  # Add a small delay before retrying
            continue

        status = data.get("data", {}).get("item", {}).get("status", None)
        origin = (
            data.get("data", {})
            .get("item", {})
            .get("stream_info", {})
            .get("replay_url", {})
            .get("origin", None)
        )

        if origin is not None:
            formatted_data = json.dumps(
                data, ensure_ascii=False, indent=4, sort_keys=True
            )
            log_path = f"./log/{date_str}.json"
            with open(log_path, "w", encoding="utf-8") as f:
                f.write(formatted_data)
            # dingtalk(dingWebHook(), origin)
            print(f"{datetime.datetime.now()} Data saved to {log_path}")
            # print(origin)
            download_m3u8(origin, date_str)
            break

        if status == 2:
            print("Status is 2, exiting loop.")
            break

        time.sleep(2)  # Avoid frequent requests
        count += 1
        print(f"Attempt {count} {date_str}\r", end="")


def get_req(
    url: str,
    proxy_addr: Union[str, None] = None,
    headers: Union[dict, None] = None,
    data: Union[dict, bytes, None] = None,
    json_data: Union[dict, list, None] = None,
    timeout: int = 20,
    abroad: bool = False,
    content_conding: str = "utf-8",
    redirect_url: bool = False,
) -> Union[str, Any]:
    if headers is None:
        headers = {}
    try:
        if proxy_addr:
            proxies = {"http": proxy_addr, "https": proxy_addr}
            if data or json_data:
                response = requests.post(
                    url,
                    data=data,
                    json=json_data,
                    headers=headers,
                    proxies=proxies,
                    timeout=timeout,
                )
            else:
                response = requests.get(
                    url, headers=headers, proxies=proxies, timeout=timeout
                )
            if redirect_url:
                return response.url
            resp_str = response.text
        else:
            if data and not isinstance(data, bytes):
                data = urllib.parse.urlencode(data).encode(content_conding)
            if json_data and isinstance(json_data, (dict, list)):
                data = json.dumps(json_data).encode(content_conding)

            req = urllib.request.Request(url, data=data, headers=headers)

            try:
                if abroad:
                    response = urllib.request.urlopen(req, timeout=timeout)
                else:
                    response = opener.open(req, timeout=timeout)
                if redirect_url:
                    return response.url
                content_encoding = response.info().get("Content-Encoding")
                try:
                    if content_encoding == "gzip":
                        with gzip.open(
                            response, "rt", encoding=content_conding
                        ) as gzipped:
                            resp_str = gzipped.read()
                    else:
                        resp_str = response.read().decode(content_conding)
                finally:
                    response.close()

            except urllib.error.HTTPError as e:
                if e.code == 400:
                    resp_str = e.read().decode(content_conding)
                else:
                    raise
            except urllib.error.URLError as e:
                print("URL Error:", e)
                raise
            except Exception as e:
                print("An error occurred:", e)
                raise

    except Exception as e:
        resp_str = str(e)

    return resp_str


def dingWebHook():
    timestamp = str(round(time.time() * 1000))
    secret = "SECda73626dcd59d9dc7da5115163190973ebcadc73cd6fb7c1103b425b2bb948cd"
    secret_enc = secret.encode("utf-8")
    string_to_sign = "{}\n{}".format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode("utf-8")
    hmac_code = hmac.new(
        secret_enc, string_to_sign_enc, digestmod=hashlib.sha256
    ).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
    return f"https://oapi.dingtalk.com/robot/send?access_token=4fb66b0bb8bff685460926a940b273df948361b97808a1187c0aa2c19293417b&timestamp={timestamp}&sign={sign}"


def dingtalk(url: str, content: str, number: Optional[str] = "") -> Dict[str, Any]:
    headers: Dict[str, str] = {"Content-Type": "application/json"}
    success = []
    error = []
    api_list = url.replace("，", ",").split(",") if url.strip() else []
    for api in api_list:
        json_data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "消息",
                "text": content,
            },
            "at": {
                "atMobiles": [number],
            },
        }
        try:
            data = json.dumps(json_data).encode("utf-8")
            req = urllib.request.Request(api, data=data, headers=headers)
            response = opener.open(req, timeout=10)
            json_str = response.read().decode("utf-8")
            json_data = json.loads(json_str)
            if json_data["errcode"] == 0:
                success.append(api)
            else:
                error.append(api)
                print(f'钉钉推送失败, 推送地址：{api}, {json_data["errmsg"]}')
                print(data)
        except Exception as e:
            error.append(api)
            print(f"钉钉推送失败, 推送地址：{api}, 错误信息:{e}")
            print(data)


def get_weibo_stream_data(
    url: str, proxy_addr: Union[str, None] = None, cookies: Union[str, None] = None
) -> Dict[str, Any]:
    headers = {
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Cookie": "XSRF-TOKEN=qAP-pIY5V4tO6blNOhA4IIOD; SUB=_2AkMRNMCwf8NxqwFRmfwWymPrbI9-zgzEieKnaDFrJRMxHRl-yT9kqmkhtRB6OrTuX5z9N_7qk9C3xxEmNR-8WLcyo2PM; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9WWemwcqkukCduUO11o9sBqA; WBPSESS=Wk6CxkYDejV3DDBcnx2LOXN9V1LjdSTNQPMbBDWe4lO2HbPmXG_coMffJ30T-Avn_ccQWtEYFcq9fab1p5RR6PEI6w661JcW7-56BszujMlaiAhLX-9vT4Zjboy1yf2l",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
    }
    if cookies:
        headers["Cookie"] = cookies

    msg_file = "./log/msg.json"
    with open(msg_file, "r", encoding="utf-8") as file:
        msg = json.load(file)

    try:
        json_str = get_req(url, proxy_addr=proxy_addr, headers=headers)
        json_data = json.loads(json_str)
    except:
        print(json_str)
        time.sleep(random.randint(60, 300))
        return

    if "data" not in json_data:
        print(f"{datetime.datetime.now()} {json_str}")
        time.sleep(random.randint(600, 800))
        return
    for i in json_data["data"]["list"]:
        if "isTop" in i and i["isTop"] == 1:
            continue
        if i["id"] in msg["ids"]:
            continue
        # print(f'{datetime.datetime.now()} --- {json.dumps(i, ensure_ascii=False, indent=4, sort_keys=True)} \n --------------------------------------------------------------------------------------')

        msg["ids"].append(i["id"])

        if i["isLongText"]:
            long_str = get_req(
                f'https://weibo.com/ajax/statuses/longtext?id={i["mblogid"]}',
                proxy_addr=proxy_addr,
                headers=headers,
            )
            long_data = json.loads(long_str)
            content = long_data["data"]["longTextContent"]
        else:
            content = i["text"]
        if i["pic_num"] > 0:
            pic_urls = []
            for j in i["pic_ids"]:
                pic_urls.append(f"![](https://wx1.sinaimg.cn/orj1080/{j}.jpg)")
            content += "\n" + " ".join(pic_urls)

        if "page_info" in i:
            content += "\n" + f"![]({i['page_info']['page_pic']})"
        if "url_struct" in i:
            for j in i["url_struct"]:
                if "pic_ids" in j:
                    for k in j["pic_ids"]:
                        content += f" ![](http://wx4.sinaimg.cn/large/{k}.jpg)"

        if "page_info" in i and i["page_info"]["object_type"] == "live":
            room_id = i["page_info"]["object_id"]
            loop_until_status_one(
                f"https://weibo.com/l/pc/anchor/live?live_id={room_id}",
                get_current_date_str(room_id.replace("1022:", "")),
            )
            continue
        elif (
            "page_info" in i
            and i["page_info"]["object_type"] != "video"
            and i["page_info"]["object_type"] != "live"
        ):
            print(i["page_info"]["object_type"])

        time.sleep(1)
        dingtalk(dingWebHook(), content)
    if len(msg["ids"]) >= 500:
        msg["ids"] = msg["ids"][20:]
    with open(msg_file, "w", encoding="utf-8") as file:
        json.dump(msg, file, ensure_ascii=False, indent=4)


def download_m3u8(url, filename):
    process = subprocess.Popen(
        [
            "D:/tools/m3u8/N_m3u8DL-CLI_v3.0.2.exe",
            url,
            "--workDir",
            "D:/tools/m3u8/冰寒直播",
            "--saveName",
            filename,
            "--noMerge",
            "--noProxy",
        ],
        stdout=subprocess.PIPE,
        text=True,
    )

    while True:
        line = process.stdout.readline()
        if not line:
            print("下载完成。。。")
            break
        # print(f'{line} \r', end='')
    process.wait()


def gzh() -> Boolean:
    return False
    cookies = {
        "Cookie": "qq_domain_video_guid_verify=98986da5ca475c3c; _qimei_uuid42=182040e050f1004a4fa9ef89b2c43af13c7cc54e99; _qimei_fingerprint=dde5685c8f300f9557d20d4ec8bde763; _qimei_q36=; _qimei_h38=2159998e4fa9ef89b2c43af102000007218204; pgv_pvid=5858915660; RK=NaXsQnnGcx; ptcz=d7ddb225422dac2c350eb329f74475c9e0ad53ba892bc86b3f8059214d9b64af; ua_id=cIhjL1gpFVexy9E5AAAAAHmKeNHh_1dT4Jcdh6RcUTo=; wxuin=31061409629502; mm_lang=zh_CN; _clck=3894973206|1|fs5|0; uuid=c9849a3e57ff6180f1b96078dc8006c0; rand_info=CAESIE2a5iofpRHboJDxTb7RNfFJ3qbX5Y0dII3d46TA2/U9; slave_bizuin=3894973206; data_bizuin=3894973206; bizuin=3894973206; data_ticket=KWSuhHri0uBbju1bCItlUsuSrlGte9iyaI2Nl4Bizw0iJ1kb85saXwW1CAtZSYOS; slave_sid=MVlINE1TZFNTZ1QyNzh4aW1WQ0NmcmhMUFkxX0syakM5Q3A1MktGUHB6eW91RVZ2RmZEMmlCcldiUldtc2hyY3hxWW9PS0hnamJOSEtMWTVidTB5NFd3VVk1MlpvN1J5Y1h2d2taWGZtWjA4c19rcUpnbkcxWjZDUmxHN1NIM1Q4MVVLNU16S3hwNFAwQ3A3; slave_user=gh_53d6a217e3b5; xid=c181c5780d5e0a0feecd2ae51e5f246c; _clsk=1m9bji7|1735543173406|2|1|mp.weixin.qq.com/weheat-agent/payload/record",
        # 其他cookie信息，请直接从浏览器里复制
    }
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="124", "Brave";v="124", "Not-A.Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
        "x-requested-with": "XMLHttpRequest",
    }
    params = {
        "sub": "list",
        "search_field": "null",
        "begin": "0",
        "count": "10",  # 采集条数
        "query": "",
        "fakeid": "MzIyMDMxOTIyOQ==",
        "type": "101_1",
        "free_publish_type": "1",
        "sub_action": "list_ex",
        "token": "1646457763",  # token值
        "lang": "zh_CN",
        "f": "json",
        "ajax": "1",
    }
    response = requests.get(
        "https://mp.weixin.qq.com/cgi-bin/appmsgpublish", params=params, cookies=cookies
    )
    resultStr = response.json()
    base_resp = resultStr.get("base_resp")
    print(f"\n\r公众号：{base_resp.get('err_msg')}")
    if base_resp.get("ret") != 0:
        return False
    publish_page = resultStr.get("publish_page")
    articleList = json.loads(publish_page)
    for article in articleList.get("publish_list"):
        publish_info = json.loads(article.get("publish_info"))
        if publish_info["sent_info"]["time"] < int(wx_lock) - 86400:
            return True
        title = publish_info["appmsgex"][0]["title"]
        link = publish_info["appmsgex"][0]["link"]
        print(title, link)
        response = requests.post(
            "http://127.0.0.1:81/gather/",
            json={
                "folder": "",
                "urls": [link],
            },
        )
        time.sleep(1)
        print(response.text)
    return False


def check_port(host, port, timeout=1):
    try:
        with socket.create_connection((host, port), timeout) as sock:
            return True
    except (socket.timeout, ConnectionRefusedError, OSError):
        return False


if __name__ == "__main__":
    host = "127.0.0.1"
    port = 81
    if check_port(host, port):
        print(f"端口 {port} 在 {host} 上是开启的。")
    else:
        print(f"端口 {port} 在 {host} 上是关闭的。")
    while True:
        now = datetime.datetime.now()
        print(f"{now} 监控中... \r", end="")

        wx_lock_path = "./log/wx.lock"
        with open(wx_lock_path, "r", encoding="utf-8") as file:
            wx_lock = file.read()
        if int(wx_lock) < now.timestamp() and gzh():
            with open(wx_lock_path, "w", encoding="utf-8") as file:
                today = datetime.date.today()
                tomorrow = today + datetime.timedelta(days=1)
                midnight_tomorrow = datetime.datetime(
                    tomorrow.year, tomorrow.month, tomorrow.day, 0, 0, 0
                )
                timestamp_midnight_tomorrow = int(midnight_tomorrow.timestamp())
                file.write(f"{timestamp_midnight_tomorrow}")

        get_weibo_stream_data(
            url="https://weibo.com/ajax/statuses/mymblog?uid=5515166761&page=1&feature=0"
        )
        # if now.hour == 12 and now.minute == 0:
        # break
        if 0 <= now.hour < 6:
            print(f"当前时间是 {now} ，休眠到6點。")
            time.sleep(3600 * (6 - now.hour))
        else:
            time.sleep(random.randint(10, 30))
# pyinstaller -F --distpath=./ .\main.py
